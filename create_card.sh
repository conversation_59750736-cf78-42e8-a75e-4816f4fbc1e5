#!/bin/bash
# Capital One Virtual Card Generator
# Script to create virtual cards using the Capital One API

# Set text colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print banner
echo -e "\n${BLUE}=================================================${NC}"
echo -e "${BLUE}    Capital One Virtual Card Generator Script    ${NC}"
echo -e "${BLUE}=================================================${NC}\n"

# Check if no arguments were provided - run in interactive mode
if [ $# -eq 0 ]; then
    echo -e "${GREEN}Running in interactive mode...${NC}"
    python cap1_card_generator.py
    exit 0
fi

# Check if explicitly requesting interactive mode
if [ "$1" == "--interactive" ]; then
    python cap1_card_generator.py --interactive
    exit 0
fi

# Check if cookies file exists
if [ ! -f "raw_cookies.txt" ]; then
    echo -e "${RED}Error: raw_cookies.txt file not found${NC}"
    echo "Please create this file with your Capital One cookies"
    echo "See README.md for instructions on how to get cookies"
    exit 1
fi

# Default values
NICKNAME="My Virtual Card"
MERCHANT=""  # Will use the script's default if not specified
AMOUNT=50.00
DAYS=30
ACCOUNT="X3AZwG7NohnZnKeUDS6BIENkSovn9L/TbE+IzH0fbvs="
COUNT=1
DELAY=2
MAX_FAILURES=3

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --nickname)
            NICKNAME="$2"
            shift 2
            ;;
        --merchant)
            MERCHANT="$2"
            shift 2
            ;;
        --amount)
            AMOUNT="$2"
            shift 2
            ;;
        --days)
            DAYS="$2"
            shift 2
            ;;
        --account)
            ACCOUNT="$2"
            shift 2
            ;;
        --count)
            COUNT="$2"
            shift 2
            ;;
        --delay)
            DELAY="$2"
            shift 2
            ;;
        --max-failures)
            MAX_FAILURES="$2"
            shift 2
            ;;
        --help)
            echo -e "${BLUE}Usage:${NC} $0 [options]"
            echo
            echo -e "${YELLOW}Interactive Mode:${NC}"
            echo "  Run without arguments or with --interactive to use interactive mode"
            echo
            echo -e "${YELLOW}Card Configuration:${NC}"
            echo "  --nickname NAME     Nickname for the virtual card (default: My Virtual Card)"
            echo "  --merchant NAME     Merchant name for the virtual card (default: General Purchase)"
            echo "  --amount AMOUNT     Amount limit for the virtual card (default: 50.00)"
            echo "  --days DAYS         Expiration days for the virtual card (default: 30)"
            echo
            echo -e "${YELLOW}Generation Options:${NC}"
            echo "  --count COUNT       Number of cards to generate (default: 1)"
            echo "  --delay DELAY       Delay in seconds between attempts (default: 2)"
            echo "  --max-failures MAX  Max consecutive failures before stopping (default: 3)"
            echo
            echo -e "${YELLOW}Advanced Options:${NC}"
            echo "  --account ID        Account reference ID (default is provided)"
            echo "  --help              Show this help message"
            echo
            echo -e "${BLUE}Examples:${NC}"
            echo "  $0                           # Run in interactive mode"
            echo "  $0 --count 5                 # Generate 5 cards"
            echo "  $0 --merchant \"Amazon\" --amount 100  # Generate a card for Amazon with $100 limit"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option:${NC} $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Run the Python script
CMD="python cap1_card_generator.py --cookies raw_cookies.txt"

# Add optional parameters if specified
CMD="$CMD --nickname \"$NICKNAME\""

if [ -n "$MERCHANT" ]; then
    CMD="$CMD --merchant \"$MERCHANT\""
fi

CMD="$CMD --amount $AMOUNT --days $DAYS --account \"$ACCOUNT\" --count $COUNT --delay $DELAY --max-failures $MAX_FAILURES"

# Execute the command
eval $CMD
