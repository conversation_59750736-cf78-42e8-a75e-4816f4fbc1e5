#!/usr/bin/env python3
"""
Install dependencies for Capital One Virtual Card Generator
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a Python package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ Failed to install {package}")
        return False

def check_chrome():
    """Check if Chrome is installed."""
    try:
        # Try to find Chrome executable
        import platform
        system = platform.system()
        
        if system == "Windows":
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
            ]
        elif system == "Darwin":  # macOS
            chrome_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            ]
        else:  # Linux
            chrome_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser"
            ]
        
        for path in chrome_paths:
            if os.path.exists(path):
                print(f"✓ Found Chrome at: {path}")
                return True
        
        print("✗ Chrome not found. Please install Google Chrome.")
        print("  Download from: https://www.google.com/chrome/")
        return False
    except Exception as e:
        print(f"✗ Error checking for Chrome: {e}")
        return False

def main():
    print("Capital One Virtual Card Generator - Dependency Installer")
    print("=" * 60)
    
    # Required packages
    packages = [
        "selenium",
        "requests"
    ]
    
    print("Installing Python packages...")
    all_installed = True
    
    for package in packages:
        if not install_package(package):
            all_installed = False
    
    print("\nChecking for Chrome browser...")
    chrome_available = check_chrome()
    
    print("\n" + "=" * 60)
    print("INSTALLATION SUMMARY")
    print("=" * 60)
    
    if all_installed and chrome_available:
        print("✓ All dependencies installed successfully!")
        print("\nYou can now run the browser automation script:")
        print("  python cap1_browser_automation.py --nickname 'My Card' --count 5")
    else:
        print("✗ Some dependencies are missing:")
        if not all_installed:
            print("  - Python packages failed to install")
        if not chrome_available:
            print("  - Chrome browser not found")
        print("\nPlease resolve the issues above before running the script.")

if __name__ == "__main__":
    main()
