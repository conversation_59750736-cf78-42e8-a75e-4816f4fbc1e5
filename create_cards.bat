@echo off
echo Capital One Virtual Card Generator - Browser Automation
echo ========================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Install dependencies if needed
echo Installing dependencies...
python install_dependencies.py

echo.
echo Starting virtual card creation...
echo.

REM Run the browser automation script with default settings
python cap1_browser_automation.py --nickname "My Virtual Card" --count 1

echo.
echo Script completed. Check the logs directory for detailed logs.
pause
