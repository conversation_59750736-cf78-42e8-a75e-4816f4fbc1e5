#!/usr/bin/env python3
"""
Capital One Virtual Card Generator

This script creates virtual cards using the Capital One API.
"""

import argparse
import json
import os
import sys
import time
import random
import requests
import logging
import datetime

# Set up logging
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_filename = f"{log_dir}/cap1_cards_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

# Configure root logger
logging.basicConfig(level=logging.INFO)

# Create custom logger
logger = logging.getLogger("cap1_cards")
logger.setLevel(logging.INFO)
logger.propagate = False  # Don't propagate to root logger

# Create handlers
file_handler = logging.FileHandler(log_filename)
console_handler = logging.StreamHandler()

# Create formatters
file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_formatter = logging.Formatter('%(levelname)s: %(message)s')

# Set formatters
file_handler.setFormatter(file_formatter)
console_handler.setFormatter(console_formatter)

# Add handlers to logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

def get_security_token(session, cookies):
    """Get the security token needed for card creation."""
    url = "https://myaccounts.capitalone.com/web-api/private/1594903/edge/security/token?level=3.1"

    headers = {
        'accept': 'application/json;v=1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6943.143 Safari/537.36',
        'referer': 'https://myaccounts.capitalone.com/VirtualCards/Manager/createVirtualCard',
        'origin': 'https://myaccounts.capitalone.com'
    }

    try:
        logger.info("Getting security token...")
        response = session.get(url, headers=headers)
        logger.info(f"Security token response status: {response.status_code}")

        if response.status_code == 200:
            try:
                # Try to parse the JSON response
                token_data = response.json()
                logger.info("Security token obtained successfully")
                return token_data
            except json.JSONDecodeError:
                # This specific error indicates invalid cookies or session expired
                logger.error("Failed to parse security token response. Your cookies are likely expired.")
                logger.error("Response content: " + response.text[:100] + "..." if len(response.text) > 100 else response.text)
                return "COOKIES_EXPIRED"
        else:
            logger.error(f"Failed to get security token: HTTP {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error getting security token: {e}")
        return None

def generate_random_hex(length):
    """Generate a random hex string of specified length."""
    return ''.join(random.choice('0123456789abcdef') for _ in range(length))

def create_virtual_card(session, cookies, token, card_data, merchant_name="General Purchase"):
    """Create a virtual card using the provided token and card data."""
    url = "https://myaccounts.capitalone.com/web-api/tiger/protected/222543/commerce-virtual-numbers"

    headers = {
        'accept': 'application/jwt;v=1',
        'content-type': 'application/jwt',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.6943.143 Safari/537.36',
        'referer': 'https://myaccounts.capitalone.com/VirtualCards/Manager/createVirtualCard',
        'origin': 'https://myaccounts.capitalone.com',
        'x-accept': 'application/json;v=1',
        'x-gw-client-public-key': 'eyJlIjoiQVFBQiIsImt0eSI6IlJTQSIsIm4iOiJtRUpGWUdqZjdBVXZINE02dWJNZ1pPNm5sTjY5S2NkeDhFVm9UZTJfc0phbm1sdTA4SFdSeTR0UGM4ZHNOV3U1Wkw0Zl9OZUQyUU4zX09Mb1Z3NERaeE1ZTk12WnlxSC1LVUxKWGg2TE1JalRzY3djZkJqTjF3SkFrbnAyMjZENHdKUENyN1U5eFNmN3AwZEhfbXhlNHpOZDIzTzhLeGJHMHFZdlJLMWRuOHdraEROVFZlU2tiY19fZlM1NV9OZ1pJSklyWXhUOUMtRHdWS0RVMlJlUzNoUmVWRzBmT29LVzdROTUwbDhtbEpIOFFSUFZ4Z1Jrb2g1ZUFNSDRVOWUtZ1NxMWQxMEtwcWV0V0pHMUhUTXRaWlhQM1hoaGpjS2tVbGszdk1QZjNxaUlVZnhlRDZFZ3NqOVFUT0lidURoNlZBOVBIZTkxaU8td0xtY0E5a1hQUlEiLCJraWQiOiI0NDQ4N2QzMS0wZjI3LTRhNzAtODI3Ny00YjM0ZTg1N2VjOWMifQ',
        'evt_synch_token': str(int(time.time() * 1000) % **************),  # Generate a timestamp-based token
        'traceparent': f'00-{generate_random_hex(32)}-{generate_random_hex(16)}-01',
        'tracestate': f'1356230@nr=0-1-1927717-*********-{generate_random_hex(16)}----{int(time.time() * 1000)}'
    }

    # For demonstration purposes, we'll use a sample JWT payload
    # In a real implementation, this would be properly generated and encrypted
    sample_jwt = "eyJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIiwia2lkIjoiNzYzZmIxNjAtNmVjYi00OTYwLWFiZjctNjcwNjI1MzZiMzJhIn0.lhwQRwXl9UcZ37XhdjInjdnil-pUIt7Cs-Z6v7vjFmtnpMEa7fJW2kWWcApijb_cU4CPmbAiuGqaqrk4HMwp3RBoM32uB4UNCg8I47Ckt-bH1L3VuaNfke8pQGLRanRAyL1EJHEKQUpunmy7fvinpJjcOSyu2C4wVXkRQUBiPryEuXEK5avP_nvP1aCKqRbNZwVwotDNTrHsTlywvp66hhp7UGx82a1w4oOtx3MNgzTD1vOgGeCfG713cye7vKj6dQ6-FviZNk_H273euybA3HvzmLV_b-iNrnnRZFhsG3jrePEsWDRly4hI_7UNSR6En-Hjgbf2SsgRgZsF-MGlJw.C1DOdCwZtt9EFOYj.WuFo7oFlUn2yZkw2vZJlwCMg6TAPUfrcI_QFvLXrF_xKUTzj2fiT3Qq6DtxD64pqBfrn-89wSRcBnZdO6kqAKGRkw_3OIQ8BHhdR2i_GdPkGccn7mAUSs04LayxNE5V4IZy5UjpN5Ry96j6fM4FKZlurR9vR1EkedbfWq1SmlPf0KuQSo3v1iWENsJhIeY87RYqPT7NGIwEPa2cqCuKbe3p2Xaz_J9wDHrX0hD8kgpslQvbRk0IpbLku-Bs9qbmSbByFXIAmzHg4yp_zz3cEZyLgZP9tdxvxfWq--BQi0lUbuGE84NPVdhVQFmA0satG2uk6qlb0g79_Yi0S5XbqJKQopl3CCbuflmu2YfasPMFEr527qF-B6NR4nrp5TMr4aLY9QM_lHFkWs-8DG8UiYAtvIP3pDPy9oP39_XPh1Q.Ow2xzHcWdCAbq3Y7ksfVkg"

    # In a real implementation, we would modify this JWT with the card details
    # For now, we'll just use the sample JWT as is
    payload = sample_jwt

    try:
        logger.info(f"Creating virtual card for merchant: {merchant_name}...")
        response = session.post(url, headers=headers, data=payload)
        logger.info(f"Create card response status: {response.status_code}")

        if response.status_code == 200:
            logger.info("Card created successfully!")

            # Check for any indication of hitting a limit
            if "limit" in response.text.lower() or "maximum" in response.text.lower() or "cap" in response.text.lower():
                logger.warning("Possible card generation limit reached.")

            return response.text
        else:
            # Provide specific error messages for common status codes
            if response.status_code == 429:
                logger.error("Rate limit exceeded. You've reached the maximum number of card generation attempts.")
            elif response.status_code == 403:
                logger.error("Access forbidden. You may have reached your card generation limit.")
            else:
                logger.error(f"Failed to create card: HTTP {response.status_code}")

            return None
    except Exception as e:
        logger.error(f"Error creating virtual card: {e}")
        return None

def load_cookies_from_file(filename):
    """Load cookies from a file."""
    try:
        with open(filename, 'r') as f:
            cookie_text = f.read().strip()

        # Parse the cookie string into a dictionary
        cookies = {}
        for cookie_pair in cookie_text.split('; '):
            if '=' in cookie_pair:
                name, value = cookie_pair.split('=', 1)
                cookies[name] = value

        return cookies
    except Exception as e:
        print(f"Error loading cookies: {e}")
        return None

def get_user_input(prompt, default=None, type_func=str, min_val=None, max_val=None):
    """Get user input with validation and default value."""
    default_str = f" [{default}]" if default is not None else ""
    while True:
        try:
            user_input = input(f"{prompt}{default_str}: ").strip()
            if user_input == "" and default is not None:
                return default

            # Convert to the appropriate type
            value = type_func(user_input)

            # Validate min/max if applicable
            if min_val is not None and value < min_val:
                print(f"Value must be at least {min_val}. Please try again.")
                continue
            if max_val is not None and value > max_val:
                print(f"Value must be at most {max_val}. Please try again.")
                continue

            return value
        except ValueError:
            print(f"Invalid input. Please enter a valid {type_func.__name__}.")

def interactive_mode():
    """Run the script in interactive mode, asking the user for settings."""
    print("\n" + "="*60)
    print(" Capital One Virtual Card Generator - Interactive Mode ".center(60, "="))
    print("="*60 + "\n")

    # Check for cookies file
    cookies_file = "raw_cookies.txt"
    if not os.path.exists(cookies_file):
        print(f"Error: {cookies_file} not found.")
        cookies_file = get_user_input("Enter path to cookies file")
        if not os.path.exists(cookies_file):
            print(f"Error: {cookies_file} still not found. Exiting.")
            sys.exit(1)

    # Ask for generation mode
    print("\nGeneration Mode:")
    print("1. Generate a specific number of cards")
    print("2. Generate cards until failure")

    mode = get_user_input("Select mode (1 or 2)", default="1", type_func=int, min_val=1, max_val=2)

    if mode == 1:
        count = get_user_input("How many cards to generate?", default=1, type_func=int, min_val=1)
        max_failures = get_user_input("Maximum consecutive failures before stopping?", default=3, type_func=int, min_val=1)
    else:
        count = 1000  # A high number, will stop on failure
        max_failures = 1  # Stop on first failure

    # Ask for card settings
    print("\nCard Settings (press Enter for defaults):")
    # Ask for nickname instead of merchant name
    nickname = get_user_input("Virtual card nickname", default="My Virtual Card")

    # Use default merchant name without asking
    merchant = "General Purchase"

    amount_input = get_user_input("Amount limit (leave empty for no limit)", default="")
    amount = float(amount_input) if amount_input else None

    days = get_user_input("Expiration days", default=30, type_func=int, min_val=1)

    # Ask for advanced settings
    print("\nAdvanced Settings (press Enter for defaults):")
    delay = get_user_input("Delay between requests (seconds)", default=2, type_func=int, min_val=1)

    # Use default account ID
    account = "X3AZwG7NohnZnKeUDS6BIENkSovn9L/TbE+IzH0fbvs="

    # Create a namespace object to mimic argparse
    class Args:
        pass

    args = Args()
    args.cookies = cookies_file
    args.merchant = merchant
    args.amount = amount
    args.days = days
    args.count = count
    args.delay = delay
    args.max_failures = max_failures
    args.account = account
    args.nickname = nickname

    return args

def main():
    """Main function to run the script."""
    # Check if running in interactive mode
    if len(sys.argv) == 1 or (len(sys.argv) == 2 and sys.argv[1] == "--interactive"):
        args = interactive_mode()
    else:
        # Command-line mode
        parser = argparse.ArgumentParser(description='Capital One Virtual Card Generator')

        # Required arguments
        parser.add_argument('--cookies', required=True, help='Path to file containing raw cookies')
        parser.add_argument('--interactive', action='store_true', help='Run in interactive mode')

        # Card configuration
        card_group = parser.add_argument_group('Card Configuration')
        card_group.add_argument('--nickname', default="My Virtual Card", help='Nickname for the virtual card (default: "My Virtual Card")')
        card_group.add_argument('--merchant', default="General Purchase", help='Merchant name (default: "General Purchase")')
        card_group.add_argument('--amount', type=float, help='Amount limit for the card')
        card_group.add_argument('--days', type=int, default=30, help='Expiration days (default: 30)')

        # Generation options
        gen_group = parser.add_argument_group('Generation Options')
        gen_group.add_argument('--count', type=int, default=1, help='Number of cards to generate (default: 1)')
        gen_group.add_argument('--delay', type=int, default=2, help='Delay in seconds between attempts (default: 2)')
        gen_group.add_argument('--max-failures', type=int, default=3, help='Max consecutive failures before stopping (default: 3)')

        # Advanced options
        adv_group = parser.add_argument_group('Advanced Options')
        adv_group.add_argument('--account', default="X3AZwG7NohnZnKeUDS6BIENkSovn9L/TbE+IzH0fbvs=",
                            help='Account reference ID (default is provided)')

        args = parser.parse_args()

    # Only print banner if not in interactive mode (interactive mode has its own banner)
    if not (len(sys.argv) == 1 or (len(sys.argv) == 2 and sys.argv[1] == "--interactive")):
        print("\n" + "="*60)
        print(" Capital One Virtual Card Generator ".center(60, "="))
        print("="*60 + "\n")

    # Load cookies
    logger.info(f"Loading cookies from {args.cookies}")
    cookies = load_cookies_from_file(args.cookies)
    if not cookies:
        logger.error("Failed to load cookies. Exiting.")
        sys.exit(1)

    # Create a session with the cookies
    session = requests.Session()
    for name, value in cookies.items():
        session.cookies.set(name, value)

    # Print configuration
    print(f"Configuration:")
    print(f"  • Cards to generate: {args.count}")
    print(f"  • Nickname: {args.nickname}")
    if args.amount:
        print(f"  • Amount limit: ${args.amount:.2f}")
    print(f"  • Expiration days: {args.days}")
    print(f"  • Delay between requests: {args.delay} seconds")
    print(f"  • Max consecutive failures: {args.max_failures}")
    print()

    # Get security token
    token = get_security_token(session, cookies)
    if token == "COOKIES_EXPIRED":
        print("\n" + "="*60)
        print(" COOKIES EXPIRED ".center(60, "="))
        print("="*60)
        print("\nYour Capital One session has expired. Please follow these steps:")
        print("1. Log in to your Capital One account in your browser")
        print("2. Open Developer Tools (F12 or right-click > Inspect)")
        print("3. Go to the Network tab")
        print("4. Navigate to the virtual cards page")
        print("5. Find the 'Cookie' header in any request")
        print("6. Copy the entire cookie string")
        print("7. Update the raw_cookies.txt file with the new cookies")
        print("\nAfter updating your cookies, run the script again.")

        # If in interactive mode, offer to update cookies now
        if len(sys.argv) == 1 or (len(sys.argv) == 2 and sys.argv[1] == "--interactive"):
            update_now = input("\nWould you like to update your cookies now? (y/n): ").strip().lower()
            if update_now == 'y' or update_now == 'yes':
                print("\nPlease paste your new cookies below (press Ctrl+D or Ctrl+Z when done):")
                new_cookies = ""
                try:
                    while True:
                        line = input()
                        new_cookies += line + "\n"
                except (EOFError, KeyboardInterrupt):
                    pass

                if new_cookies.strip():
                    # Save the new cookies
                    with open(args.cookies, 'w') as f:
                        f.write(new_cookies.strip())
                    print(f"\nCookies saved to {args.cookies}")
                    print("Restarting the script with new cookies...")

                    # Reload cookies and try again
                    cookies = load_cookies_from_file(args.cookies)
                    if not cookies:
                        logger.error("Failed to load updated cookies. Exiting.")
                        sys.exit(1)

                    # Create a new session with the updated cookies
                    session = requests.Session()
                    for name, value in cookies.items():
                        session.cookies.set(name, value)

                    # Try to get the security token again
                    token = get_security_token(session, cookies)
                    if token == "COOKIES_EXPIRED" or not token:
                        logger.error("Still unable to get security token with updated cookies. Exiting.")
                        sys.exit(1)
                else:
                    print("No cookies provided. Exiting.")
                    sys.exit(1)
            else:
                print("Exiting. Please update your cookies and run the script again.")
                sys.exit(1)
        else:
            sys.exit(1)
    elif not token:
        logger.error("Failed to get security token. Exiting.")
        sys.exit(1)

    # Prepare card data
    card_data = {
        "merchantName": args.merchant,
        "accountId": args.account,
        "nickname": args.nickname
    }

    if args.amount:
        card_data["amountLimit"] = args.amount

    if args.days:
        card_data["expirationDays"] = args.days

    # Create the virtual cards
    successful_cards = 0
    failed_cards = 0
    consecutive_failures = 0

    print(f"Starting card generation process...")
    print("-" * 60)

    for i in range(args.count):
        print(f"Card {i+1}/{args.count}: ", end="", flush=True)
        logger.info(f"Creating card {i+1} of {args.count}...")

        # Create the virtual card
        result = create_virtual_card(session, cookies, token, json.dumps(card_data), args.merchant)

        if result:
            successful_cards += 1
            consecutive_failures = 0
            print("✓ Success")
            logger.info(f"Card {i+1} created successfully!")
        else:
            failed_cards += 1
            consecutive_failures += 1
            print("✗ Failed")
            logger.error(f"Failed to create card {i+1}")

            # Check if we should continue or stop due to hitting a limit
            if consecutive_failures >= args.max_failures:
                print(f"\nStopping after {consecutive_failures} consecutive failures")
                logger.warning(f"Stopping after {consecutive_failures} consecutive failures")
                break

        # Add delay between requests to avoid rate limiting
        if i < args.count - 1:
            remaining = args.count - i - 1
            print(f"Waiting {args.delay}s before next request... ({remaining} card(s) remaining)")
            time.sleep(args.delay)

    # Summary
    print("\n" + "="*60)
    print(" Generation Summary ".center(60, "="))
    print("="*60)
    print(f"Total cards attempted:  {i+1} of {args.count}")
    print(f"Successfully created:   {successful_cards}")
    print(f"Failed:                 {failed_cards}")
    print("-"*60)
    print(f"Log file: {log_filename}")
    print("="*60)

    # Log summary
    logger.info("=== Card Generation Summary ===")
    logger.info(f"Total cards attempted: {i+1} of {args.count}")
    logger.info(f"Successfully created: {successful_cards}")
    logger.info(f"Failed: {failed_cards}")
    logger.info(f"Log file: {log_filename}")

if __name__ == "__main__":
    main()
