#!/usr/bin/env python3
"""
Capital One Virtual Card Creator

This script creates virtual cards using the Capital One API.
"""

import argparse
import json
import os
import sys
import time
from datetime import datetime
import requests

class CapitalOneAPI:
    """Class to interact with the Capital One API for virtual card creation."""

    BASE_URL = "https://myaccounts.capitalone.com/web-api/private/25419"

    def __init__(self, cookies_file=None, headers_file=None):
        """
        Initialize the API client.

        Args:
            cookies_file (str): Path to a JSON file containing cookies
            headers_file (str): Path to a JSON file containing headers
        """
        self.session = requests.Session()
        self.cookies = {}
        self.headers = {
            'accept': 'application/json;v=2',
            'accept-language': 'en-US,en;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://myaccounts.capitalone.com',
            'referer': 'https://myaccounts.capitalone.com/VirtualCards/Manager/createVirtualCard?cardRef=X3AZwG7NohnZnKeUDS6BIENkSovn9L%2FTbE%2BIzH0fbvs%3D&analyticsTag=from_l1_widget&pageIndex=0&pageSize=50&account=X3AZwG7NohnZnKeUDS6BIENkSovn9L%2FTbE%2BIzH0fbvs%3D',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6834.210 Safari/537.36',
            'traceparent': '00-cb6cb9af471cf39838b3f206f96a99c2-1b1fe6b4aa4b4f7c-01',
            'tracestate': '1356230@nr=0-1-1927717-*********-1b1fe6b4aa4b4f7c----*************',
        }

        # Load cookies from file if provided
        if cookies_file and os.path.exists(cookies_file):
            try:
                with open(cookies_file, 'r') as f:
                    self.cookies = json.load(f)
                    for cookie_name, cookie_value in self.cookies.items():
                        self.session.cookies.set(cookie_name, cookie_value)
                print(f"Loaded cookies from {cookies_file}")
            except Exception as e:
                print(f"Error loading cookies: {e}")
                sys.exit(1)

        # Load headers from file if provided
        if headers_file and os.path.exists(headers_file):
            try:
                with open(headers_file, 'r') as f:
                    custom_headers = json.load(f)
                    self.headers.update(custom_headers)
                print(f"Loaded headers from {headers_file}")
            except Exception as e:
                print(f"Error loading headers: {e}")
                sys.exit(1)

    def check_authentication(self):
        """
        Check if the current session is authenticated.

        Returns:
            bool: True if authenticated, False otherwise
        """
        url = "https://myaccounts.capitalone.com/ease/user-profile"

        try:
            print("Checking authentication status...")
            response = self.session.get(url, headers={
                'accept': 'application/json',
                'user-agent': self.headers['user-agent']
            })

            print(f"Authentication check status: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'firstName' in data:
                        print(f"Authenticated as: {data.get('firstName', '')} {data.get('lastName', '')}")
                        return True
                except:
                    pass

            print("Not authenticated or session expired. Please update your cookies.")
            return False
        except Exception as e:
            print(f"Error checking authentication: {e}")
            return False

    def get_existing_cards(self, reference_id):
        """
        Get existing virtual cards.

        Args:
            reference_id (str): The account reference ID

        Returns:
            dict: API response containing existing cards
        """
        url = f"{self.BASE_URL}/commerce-virtual-numbers?limit=50&offset=0&excludeUnbound=true"

        payload = {
            "referenceId": reference_id,
            "referenceIdType": "ACCOUNT",
            "tokenStatus": [],
            "filterCriteria": [],
            "sortCriteria": []
        }

        try:
            print(f"Making request to: {url}")
            print(f"With payload: {json.dumps(payload, indent=2)}")
            response = self.session.post(url, headers=self.headers, json=payload)
            print(f"Response status: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")
            print(f"Response body: {response.text[:500]}..." if len(response.text) > 500 else f"Response body: {response.text}")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error getting existing cards: {e}")
            if hasattr(e, 'response') and e.response:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            return None

    def create_virtual_card(self, reference_id, merchant_name, amount_limit=None, expiration_days=None):
        """
        Create a new virtual card.

        Args:
            reference_id (str): The account reference ID
            merchant_name (str): Name to associate with the virtual card
            amount_limit (float, optional): Maximum amount for the card
            expiration_days (int, optional): Number of days until expiration

        Returns:
            dict: API response containing the new card details
        """
        # Using the exact endpoint from the provided request
        url = f"{self.BASE_URL}/commerce-virtual-numbers"

        # First, we need to get the existing cards to ensure we're using the correct endpoint
        existing_cards_response = self.get_existing_cards(reference_id)
        if not existing_cards_response:
            print("Failed to get existing cards, which is needed before creating a new card")
            return None

        # Now create the new card using the same endpoint but with different payload
        url = f"{self.BASE_URL}/commerce-virtual-numbers/create"

        # Using the payload structure based on the provided information
        payload = {
            "referenceId": reference_id,
            "referenceIdType": "ACCOUNT",
            "merchantName": merchant_name
        }

        if amount_limit:
            payload["amountLimit"] = float(amount_limit)

        if expiration_days:
            payload["expirationDays"] = int(expiration_days)

        try:
            print(f"Making request to: {url}")
            print(f"With payload: {json.dumps(payload, indent=2)}")
            response = self.session.post(url, headers=self.headers, json=payload)
            print(f"Response status: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")
            print(f"Response body: {response.text[:500]}..." if len(response.text) > 500 else f"Response body: {response.text}")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error creating virtual card: {e}")
            if hasattr(e, 'response') and e.response:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            return None

def save_cookies_to_file(session, filename):
    """Save session cookies to a file."""
    cookies_dict = {cookie.name: cookie.value for cookie in session.cookies}
    with open(filename, 'w') as f:
        json.dump(cookies_dict, f, indent=2)
    print(f"Cookies saved to {filename}")

def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description='Create Capital One virtual cards')
    parser.add_argument('--cookies', help='Path to JSON file containing cookies')
    parser.add_argument('--headers', help='Path to JSON file containing headers')
    parser.add_argument('--reference-id', required=True, help='Account reference ID')
    parser.add_argument('--merchant', required=True, help='Merchant name for the virtual card')
    parser.add_argument('--limit', type=float, help='Amount limit for the virtual card')
    parser.add_argument('--days', type=int, help='Expiration days for the virtual card')
    parser.add_argument('--list', action='store_true', help='List existing virtual cards')
    parser.add_argument('--save-cookies', help='Save cookies to the specified file after operation')

    args = parser.parse_args()

    # Initialize the API client
    api = CapitalOneAPI(cookies_file=args.cookies, headers_file=args.headers)

    # Check authentication
    if not api.check_authentication():
        print("Authentication failed. Please update your cookies and try again.")
        sys.exit(1)

    # List existing cards if requested
    if args.list:
        print("Fetching existing virtual cards...")
        cards = api.get_existing_cards(args.reference_id)
        if cards:
            print(json.dumps(cards, indent=2))
        return

    # Create a new virtual card
    print(f"Creating virtual card for merchant: {args.merchant}")
    result = api.create_virtual_card(
        args.reference_id,
        args.merchant,
        amount_limit=args.limit,
        expiration_days=args.days
    )

    if result:
        print("Virtual card created successfully:")
        print(json.dumps(result, indent=2))

    # Save cookies if requested
    if args.save_cookies:
        save_cookies_to_file(api.session, args.save_cookies)

if __name__ == "__main__":
    main()
