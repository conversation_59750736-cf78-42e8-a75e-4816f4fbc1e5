#!/bin/bash
# Example script showing how to use the Capital One Virtual Card Creator

# Step 1: Extract cookies from a raw cookie header
echo "Extracting cookies..."
python extract_cookies.py --input raw_cookies.txt --output cookies.json --format header

# Step 2: Extract headers from a raw header file
echo "Extracting headers..."
python extract_headers.py --input raw_headers.txt --output headers.json

# Step 3: List existing virtual cards
echo "Listing existing virtual cards..."
python cap1_virtual_card_creator.py \
  --cookies cookies.json \
  --headers headers.json \
  --reference-id "X3AZwG7NohnZnKeUDS6BIENkSovn9L/TbE+IzH0fbvs=" \
  --merchant "Example" \
  --list

# Step 4: Create a new virtual card
echo "Creating a new virtual card..."
python cap1_virtual_card_creator.py \
  --cookies cookies.json \
  --headers headers.json \
  --reference-id "X3AZwG7NohnZnKeUDS6BIENkSovn9L/TbE+IzH0fbvs=" \
  --merchant "Amazon" \
  --limit 50.00 \
  --days 30 \
  --save-cookies updated_cookies.json
