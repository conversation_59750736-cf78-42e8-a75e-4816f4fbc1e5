# Capital One Virtual Card Generator

A tool for creating virtual cards using Capital One's web interface. Due to Capital One's JWT encryption requirements, this tool now uses browser automation for reliable card creation.

## Features

- Generate multiple virtual cards with a single command
- Customizable merchant name, amount limit, and expiration
- Smart error handling with automatic stopping on failures
- Interactive cookie management when session expires
- Clean, user-friendly interface
- Comprehensive logging

## Requirements

- Python 3.6+
- Google Chrome browser
- `selenium` and `requests` libraries

## Installation

1. Clone this repository or download the scripts
2. Install the required dependencies:

```bash
python install_dependencies.py
```

Or manually install:
```bash
pip install selenium requests
```

3. Make sure Google Chrome is installed on your system

## Important Update: JWT Encryption Issue

**The original API-based script (`cap1_card_generator.py`) is currently not working** due to Capital One's implementation of JWT encryption. The API now requires:

- RSA-OAEP-256 encryption with AES-256-GCM
- Dynamic encryption keys
- Properly encrypted JWT payloads

This level of encryption is complex to reverse engineer and implement reliably.

## New Solution: Browser Automation

We've created a new browser automation script (`cap1_browser_automation.py`) that works by automating the web interface instead of calling the API directly. This approach:

- ✅ Works reliably with current Capital One security measures
- ✅ Handles all encryption automatically through the browser
- ✅ Supports all the same features (nickname, amount limits, etc.)
- ✅ Provides the same logging and error handling

## Usage

### Browser Automation Script (Recommended)

Use the new browser automation script for reliable card creation:

```bash
python cap1_browser_automation.py --nickname "My Card" --count 5
```

#### How it works:
1. Opens a Chrome browser window
2. You manually log in to Capital One (one time)
3. Navigate to the Virtual Cards section
4. The script automates the card creation process

#### Available Options:

```bash
python cap1_browser_automation.py --help
```

**Card Configuration:**
- `--nickname NAME`: Nickname for the virtual card (default: "My Virtual Card")
- `--amount AMOUNT`: Amount limit for the card (optional)
- `--days DAYS`: Expiration days (default: 30)

**Generation Options:**
- `--count COUNT`: Number of cards to generate (default: 1)
- `--delay DELAY`: Delay between card creations in seconds (default: 3)

**Browser Options:**
- `--headless`: Run browser in headless mode (not recommended for first use)

#### Examples:

```bash
# Create 5 cards with default settings
python cap1_browser_automation.py --count 5

# Create cards with custom nickname and amount limit
python cap1_browser_automation.py --nickname "Shopping Card" --amount 100.00 --count 3

# Create a single card with 60-day expiration
python cap1_browser_automation.py --nickname "Travel Card" --days 60
```

### Legacy API Script (Currently Not Working)

The original API-based script is included for reference but **does not currently work** due to JWT encryption requirements:

```bash
# This will not work due to encryption issues
python cap1_card_generator.py --nickname "My Card" --count 5
```

## How the Browser Automation Works

The browser automation script performs the following steps:

1. Opens a Chrome browser window
2. Navigates to Capital One's website
3. Waits for you to manually log in (one-time setup)
4. Automates the virtual card creation process through the web interface
5. Fills in card details (nickname, amount, expiration)
6. Submits the form and verifies creation
7. Repeats for multiple cards with delays between requests

### Error Handling

The script includes several features to handle Capital One's card generation limits:

1. **Consecutive Failure Detection**: Stops after a configurable number of consecutive failures
2. **Rate Limit Detection**: Identifies when you've hit API rate limits
3. **Configurable Delay**: Adjustable delay between requests to avoid rate limiting

### Cookie Management

The script provides smart handling of authentication:

1. **Expired Session Detection**: Automatically detects when your Capital One session has expired
2. **Interactive Cookie Updates**: In interactive mode, allows you to update cookies without restarting
3. **Guided Instructions**: Provides step-by-step instructions for obtaining new cookies
4. **Immediate Retry**: After updating cookies, automatically retries the operation

### Logging

The script creates organized log files in the `logs` directory with:

- Success/failure information for each card
- Error details when card creation fails
- Summary statistics of the generation process

## Disclaimer

This tool is for educational purposes only. Use at your own risk and in accordance with Capital One's terms of service.

## License

MIT
