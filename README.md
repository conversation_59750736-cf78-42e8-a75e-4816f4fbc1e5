# Capital One Virtual Card Generator

A streamlined tool for creating virtual cards using the Capital One API.

## Features

- Generate multiple virtual cards with a single command
- Customizable merchant name, amount limit, and expiration
- Smart error handling with automatic stopping on failures
- Interactive cookie management when session expires
- Clean, user-friendly interface
- Comprehensive logging

## Requirements

- Python 3.6+
- `requests` library

## Installation

1. Clone this repository or download the scripts
2. Install the required dependencies:

```bash
pip install requests
```

## Authentication

The script requires authentication cookies from an existing Capital One session:

1. Log in to your Capital One account in your browser
2. Open Developer Tools (F12 or right-click > Inspect)
3. Go to the Network tab
4. Navigate to the virtual cards page
5. Find the "Cookie" header in any request to Capital One
6. Copy the entire cookie string
7. Save it to a file named `raw_cookies.txt` in the same directory as the script

## Usage

### Interactive Mode (Recommended)

The easiest way to use the script is in interactive mode, which guides you through the process:

```bash
./create_card.sh
```

This will:
1. Ask you whether to generate a specific number of cards or continue until failure
2. Prompt for card settings like merchant name and amount limit
3. Guide you through the generation process with real-time feedback

### Command-Line Mode

You can also use command-line arguments for automation:

```bash
./create_card.sh --count 5
```

This will create 5 virtual cards with default settings.

### Customizing Cards

You can customize the cards with various options:

```bash
./create_card.sh --nickname "Amazon Card" --amount 100.00 --days 60
```

### All Available Options

Run the help command to see all available options:

```bash
./create_card.sh --help
```

#### Interactive Mode
- Run without arguments or with `--interactive` to use interactive mode

#### Card Configuration
- `--nickname NAME`: Nickname for the virtual card (default: "My Virtual Card")
- `--amount AMOUNT`: Amount limit for the card (default: 50.00)
- `--days DAYS`: Expiration days (default: 30)

#### Generation Options
- `--count COUNT`: Number of cards to generate (default: 1)
- `--delay DELAY`: Delay in seconds between attempts (default: 2)
- `--max-failures MAX`: Max consecutive failures before stopping (default: 3)

#### Advanced Options
- `--account ID`: Account reference ID (default is provided)

### Using the Python Script Directly

You can also use the Python script directly:

```bash
# Interactive mode
python cap1_card_generator.py

# Command-line mode
python cap1_card_generator.py --cookies raw_cookies.txt --nickname "Shopping Card" --count 5
```

## How it Works

The script performs the following steps:

1. Loads cookies from the provided file
2. Gets a security token from the Capital One API
3. Creates virtual cards using the provided parameters
4. Provides real-time feedback on the generation process
5. Handles errors and rate limiting

### Error Handling

The script includes several features to handle Capital One's card generation limits:

1. **Consecutive Failure Detection**: Stops after a configurable number of consecutive failures
2. **Rate Limit Detection**: Identifies when you've hit API rate limits
3. **Configurable Delay**: Adjustable delay between requests to avoid rate limiting

### Cookie Management

The script provides smart handling of authentication:

1. **Expired Session Detection**: Automatically detects when your Capital One session has expired
2. **Interactive Cookie Updates**: In interactive mode, allows you to update cookies without restarting
3. **Guided Instructions**: Provides step-by-step instructions for obtaining new cookies
4. **Immediate Retry**: After updating cookies, automatically retries the operation

### Logging

The script creates organized log files in the `logs` directory with:

- Success/failure information for each card
- Error details when card creation fails
- Summary statistics of the generation process

## Disclaimer

This tool is for educational purposes only. Use at your own risk and in accordance with Capital One's terms of service.

## License

MIT
