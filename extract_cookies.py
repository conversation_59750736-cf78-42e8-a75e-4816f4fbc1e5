#!/usr/bin/env python3
"""
Extract cookies from a browser's cookie export or from a raw cookie header.
Saves them in a format usable by the Capital One virtual card creator script.
"""

import argparse
import json
import sys
import re

def parse_cookie_header(cookie_header):
    """Parse a raw Cookie header into a dictionary."""
    cookies = {}
    pairs = cookie_header.split(';')
    for pair in pairs:
        if '=' in pair:
            name, value = pair.strip().split('=', 1)
            cookies[name] = value
    return cookies

def main():
    parser = argparse.ArgumentParser(description='Extract cookies from browser export or raw header')
    parser.add_argument('--input', '-i', required=True, help='Input file containing cookies or raw cookie header')
    parser.add_argument('--output', '-o', required=True, help='Output JSON file to save cookies')
    parser.add_argument('--format', '-f', choices=['netscape', 'json', 'header'], default='header',
                        help='Format of the input file (default: header)')
    
    args = parser.parse_args()
    
    cookies = {}
    
    try:
        with open(args.input, 'r') as f:
            content = f.read()
            
            if args.format == 'header':
                cookies = parse_cookie_header(content)
            elif args.format == 'json':
                # Assuming JSON format from browser export
                cookie_data = json.loads(content)
                for cookie in cookie_data:
                    if 'name' in cookie and 'value' in cookie:
                        cookies[cookie['name']] = cookie['value']
            elif args.format == 'netscape':
                # Netscape cookie format (used by curl)
                for line in content.splitlines():
                    if not line.startswith('#') and '\t' in line:
                        parts = line.split('\t')
                        if len(parts) >= 7:
                            name = parts[5]
                            value = parts[6]
                            cookies[name] = value
        
        with open(args.output, 'w') as f:
            json.dump(cookies, f, indent=2)
            print(f"Cookies saved to {args.output}")
    
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
