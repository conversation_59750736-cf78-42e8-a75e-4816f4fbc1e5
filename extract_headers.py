#!/usr/bin/env python3
"""
Extract headers from a raw HTTP request.
Saves them in a format usable by the Capital One virtual card creator script.
"""

import argparse
import json
import sys
import re

def parse_headers(raw_headers):
    """Parse raw HTTP headers into a dictionary."""
    headers = {}
    lines = raw_headers.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        if line and ':' in line:
            name, value = line.split(':', 1)
            name = name.strip()
            value = value.strip()
            
            # Skip pseudo-headers that start with colon
            if not name.startswith(':'):
                headers[name] = value
    
    return headers

def main():
    parser = argparse.ArgumentParser(description='Extract headers from raw HTTP request')
    parser.add_argument('--input', '-i', required=True, help='Input file containing raw HTTP headers')
    parser.add_argument('--output', '-o', required=True, help='Output JSON file to save headers')
    
    args = parser.parse_args()
    
    try:
        with open(args.input, 'r') as f:
            content = f.read()
            headers = parse_headers(content)
        
        with open(args.output, 'w') as f:
            json.dump(headers, f, indent=2)
            print(f"Headers saved to {args.output}")
    
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
