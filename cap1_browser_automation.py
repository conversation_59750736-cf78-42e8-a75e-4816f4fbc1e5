#!/usr/bin/env python3
"""
Capital One Virtual Card Generator - Browser Automation Version
This version uses Selenium to automate the browser and create virtual cards
through the web interface, avoiding the need to handle JWT encryption.
"""

import time
import logging
import argparse
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Set up logging
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_filename = os.path.join(log_dir, f"cap1_browser_automation_{timestamp}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CapitalOneVirtualCardGenerator:
    def __init__(self, headless=False):
        self.driver = None
        self.wait = None
        self.headless = headless
        
    def setup_driver(self):
        """Set up the Chrome WebDriver."""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("Chrome WebDriver initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            return False
    
    def login_required_message(self):
        """Display login instructions."""
        print("\n" + "="*60)
        print(" MANUAL LOGIN REQUIRED ".center(60, "="))
        print("="*60)
        print("\nPlease follow these steps:")
        print("1. A browser window will open")
        print("2. Log in to your Capital One account manually")
        print("3. Navigate to the Virtual Cards section")
        print("4. Return to this terminal and press Enter to continue")
        print("\nThe script will then automate the card creation process.")
        print("="*60)
        
    def wait_for_login(self):
        """Wait for user to manually log in."""
        self.driver.get("https://myaccounts.capitalone.com/")
        
        self.login_required_message()
        input("\nPress Enter after you've logged in and navigated to Virtual Cards...")
        
        # Verify we're on the right page
        try:
            # Look for virtual card related elements
            virtual_card_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Virtual') and contains(text(), 'Card')]")
            if virtual_card_elements:
                logger.info("Successfully detected Virtual Cards page")
                return True
            else:
                logger.warning("Could not detect Virtual Cards page. Please make sure you're on the correct page.")
                return False
        except Exception as e:
            logger.error(f"Error verifying page: {e}")
            return False
    
    def create_virtual_card(self, nickname="My Virtual Card", amount=None, days=30):
        """Create a virtual card using browser automation."""
        try:
            logger.info(f"Creating virtual card with nickname: {nickname}")
            
            # Look for "Create Virtual Card" button
            create_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'Create') and contains(text(), 'Virtual')]")
            if not create_buttons:
                create_buttons = self.driver.find_elements(By.XPATH, "//a[contains(text(), 'Create') and contains(text(), 'Virtual')]")
            
            if not create_buttons:
                logger.error("Could not find 'Create Virtual Card' button")
                return False
            
            # Click the create button
            create_buttons[0].click()
            logger.info("Clicked 'Create Virtual Card' button")
            time.sleep(2)
            
            # Fill in the nickname
            try:
                nickname_inputs = self.driver.find_elements(By.XPATH, "//input[@placeholder='Enter nickname' or @aria-label='Nickname' or contains(@name, 'nickname')]")
                if nickname_inputs:
                    nickname_inputs[0].clear()
                    nickname_inputs[0].send_keys(nickname)
                    logger.info(f"Entered nickname: {nickname}")
                else:
                    logger.warning("Could not find nickname input field")
            except Exception as e:
                logger.warning(f"Error entering nickname: {e}")
            
            # Fill in amount limit if specified
            if amount:
                try:
                    amount_inputs = self.driver.find_elements(By.XPATH, "//input[@placeholder='Amount' or @aria-label='Amount' or contains(@name, 'amount')]")
                    if amount_inputs:
                        amount_inputs[0].clear()
                        amount_inputs[0].send_keys(str(amount))
                        logger.info(f"Entered amount limit: ${amount}")
                except Exception as e:
                    logger.warning(f"Error entering amount: {e}")
            
            # Fill in expiration days if different from default
            if days != 30:
                try:
                    days_inputs = self.driver.find_elements(By.XPATH, "//input[@placeholder='Days' or @aria-label='Days' or contains(@name, 'days')]")
                    if days_inputs:
                        days_inputs[0].clear()
                        days_inputs[0].send_keys(str(days))
                        logger.info(f"Entered expiration days: {days}")
                except Exception as e:
                    logger.warning(f"Error entering days: {e}")
            
            # Submit the form
            submit_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'Create') or contains(text(), 'Submit') or @type='submit']")
            if submit_buttons:
                submit_buttons[0].click()
                logger.info("Clicked submit button")
                time.sleep(3)
                
                # Check for success
                success_indicators = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'success') or contains(text(), 'created') or contains(text(), 'Success')]")
                if success_indicators:
                    logger.info("Virtual card created successfully!")
                    return True
                else:
                    logger.warning("Could not confirm card creation success")
                    return True  # Assume success if no error
            else:
                logger.error("Could not find submit button")
                return False
                
        except Exception as e:
            logger.error(f"Error creating virtual card: {e}")
            return False
    
    def close(self):
        """Close the browser."""
        if self.driver:
            self.driver.quit()
            logger.info("Browser closed")

def main():
    parser = argparse.ArgumentParser(description='Capital One Virtual Card Generator - Browser Automation')
    parser.add_argument('--nickname', default="My Virtual Card", help='Nickname for the virtual card')
    parser.add_argument('--amount', type=float, help='Amount limit for the card')
    parser.add_argument('--days', type=int, default=30, help='Expiration days')
    parser.add_argument('--count', type=int, default=1, help='Number of cards to generate')
    parser.add_argument('--delay', type=int, default=3, help='Delay between card creations (seconds)')
    parser.add_argument('--headless', action='store_true', help='Run browser in headless mode')
    
    args = parser.parse_args()
    
    generator = CapitalOneVirtualCardGenerator(headless=args.headless)
    
    try:
        if not generator.setup_driver():
            return
        
        if not generator.wait_for_login():
            return
        
        successful_cards = 0
        failed_cards = 0
        
        for i in range(args.count):
            logger.info(f"Creating card {i+1} of {args.count}")
            
            # Create unique nickname for each card
            card_nickname = f"{args.nickname} {i+1}" if args.count > 1 else args.nickname
            
            if generator.create_virtual_card(nickname=card_nickname, amount=args.amount, days=args.days):
                successful_cards += 1
                logger.info(f"Card {i+1} created successfully")
            else:
                failed_cards += 1
                logger.error(f"Failed to create card {i+1}")
            
            # Delay between cards
            if i < args.count - 1:
                logger.info(f"Waiting {args.delay} seconds before next card...")
                time.sleep(args.delay)
        
        # Summary
        print("\n" + "="*60)
        print(" GENERATION SUMMARY ".center(60, "="))
        print("="*60)
        print(f"Total cards attempted:  {args.count}")
        print(f"Successfully created:   {successful_cards}")
        print(f"Failed:                 {failed_cards}")
        print(f"Log file: {log_filename}")
        print("="*60)
        
    finally:
        generator.close()

if __name__ == "__main__":
    main()
